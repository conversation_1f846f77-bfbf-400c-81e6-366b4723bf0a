<div class="flex-column">
  <div class="position-relative field-rupees-tag">
    <form-errors-wrapper>
      <div class="rupees-sm icon ic-calendar ic-xxs ic-coal"></div>
      <input type="text" [value]="getSelectedPossessionDisplayName() || ''" id="inpPossessionDate"
        placeholder="Select possession" (click)="toggleModal()" autocomplete="off" readonly class="h-32">
    </form-errors-wrapper>
  </div>
  <!-- Possession Modal -->
  <div class="position-relative">
    <div class="position-absolute box-shadow-10 bg-white br-4 top-0 w-100 z-index-1001" *ngIf="isOpenPossessionModal">
      <div class="mr-20 bg-white br-4">
        <div class="d-flex">
          <div class="w-100 bg-white br-4">
            <!-- Possession Options -->
            <ng-container *ngFor="let type of dateFilterList">
              <div class="form-check form-check-inline p-6 w-fit-content">
                <input type="radio" id="inpShowData{{type.value}}" name="globalRange" [(ngModel)]="selectedPossession"
                  [value]="type.value" [ngModelOptions]="{standalone: true}" 
                  class="radio-check-input w-10 h-10" (ngModelChange)="handlePossessionRangeChange($event)">
                <label class="text-dark-gray text-large ml-8"
                  for="inpShowData{{type.value}}">{{type.displayName}}</label>
              </div>
            </ng-container>
            <!-- From Possession Date and To Possession Date -->
            <div *ngIf="selectedPossession === 'Custom Date'"
              class="position-relative dashboard-filter form-group m-6 mb-16"
              [ngClass]="{'border-red-30': !customDateValidation && isSearchClicked}">
              <div class="mb-10">
                <div class="text-dark-gray text-large">From Possession Date</div>
                <div class="date-picker align-center py-4 rounded w-100">
                  <input type="text" readonly [owlDateTimeTrigger]="dtFromPossession" [owlDateTime]="dtFromPossession"
                    class="pl-20 ph-pl-12 text-large ph-w-150px" placeholder="Select date"
                    (ngModelChange)="fromPossessionDateChange($event)" [ngModelOptions]="{standalone: true}"
                    [ngModel]="getFormValue(formControlNames.fromDate)" />
                  <owl-date-time [pickerType]="'calendar'" #dtFromPossession [startAt]="currentDate"
                    [startView]="'year'" [yearOnly]="true" (afterPickerOpen)="onPickerOpened(currentDate, 'month')"
                    (monthSelected)="fromMonthChanged($event)"></owl-date-time>
                </div>
              </div>
              <div>
                <div class="text-dark-gray text-large">To Possession Date</div>
                <div class="date-picker align-center py-4 rounded w-100">
                  <input type="text" readonly [owlDateTimeTrigger]="dtToPossession" [owlDateTime]="dtToPossession"
                    class="pl-20 ph-pl-12 text-large ph-w-150px" placeholder="Select date"
                    (ngModelChange)="toPossessionDateChange($event)" [ngModelOptions]="{standalone: true}"
                    [ngModel]="getFormValue(formControlNames.toDate)" [min]="minToDate()" />
                  <owl-date-time [pickerType]="'calendar'" #dtToPossession
                    [startAt]="minToPossessionDate || currentDate" [startView]="'year'" [yearOnly]="true"
                    (afterPickerOpen)="onPickerOpened(minToDate() || currentDate, 'month')"
                    (monthSelected)="toMonthChanged($event)"></owl-date-time>
                </div>
              </div>
            </div>
            <div *ngIf="selectedPossession === 'Custom Date' && !customDateValidation && isSearchClicked"
              class="text-xs text-red fw-semi-bold mb-10 px-12">
              Please select both From and To Possession dates
            </div>
          </div>
        </div>
        <div class="flex-end p-6 border-top">
          <div class="btn-coal" (click)="possessionCloseModal()">Close</div>
        </div>
      </div>
    </div>
  </div>
</div>